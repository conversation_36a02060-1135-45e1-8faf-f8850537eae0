import { MaterialIcons } from '@expo/vector-icons';
import React from 'react';
import { Platform, Text as RNText, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import {
  Appbar,
  Avatar,
  Card,
  Text,
  useTheme
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

// 主頁面組件 - 實現 CareComms 應用的主界面
export default function HomeScreen() {
  // 獲取當前主題以支持 Dark Mode
  const theme = useTheme();

  // 處理卡片點擊事件
  const handleCardPress = (cardType: string) => {
    console.log(`${cardType} card pressed`);
    // TODO: 實現導航邏輯
  };

  // 處理快速操作按鈕點擊
  const handleQuickAction = (actionType: string) => {
    console.log(`${actionType} action pressed`);
    // TODO: 實現相應的功能
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* 狀態欄背景 */}
      <View style={[styles.statusBarBackground, { backgroundColor: theme.colors.primary }]} />
      <SafeAreaView style={styles.safeArea} edges={['bottom', 'left', 'right']}>
        {/* 頂部標題欄 */}
        <Appbar.Header style={[styles.header, { backgroundColor: theme.colors.primary }]}>
          <View style={styles.headerContent}>
            <View style={styles.logoContainer}>
              <Avatar.Image
                size={40}
                source={{ uri: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCFiSh23snutDa79cLr4-HtIJZ6A4tk8Wbqhy7HOW1-uH8cgVMVxnK0VCXWFNtMYLIvT9eYoiGhg97iHB5uFNyUqqAjgEaNGjpxIxUw72XXJ_sxHuiCgdTmxqss-M6poAh1cHmu4Ynndc2REpshiKY2UvnfNpo6fecP4ylaRIZfd-6v3sGqgo0wvzh4VLFbJVn9D1qHBoGJoz2FSzEaYzYNJ2CwGykhxOzfIVyYVzz0xnedvU3ZyCePOdzobyJtoz8mjPsEsqwd4Es' }}
                style={styles.logo}
              />
              <Text style={[styles.appTitle, {
                fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
                fontWeight: 'bold',
                color: theme.colors.onPrimary,
              }]}>
                CareComms
              </Text>
            </View>
          </View>
        </Appbar.Header>

        {/* 主要內容區域 */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* 功能卡片網格 */}
          <View style={styles.cardGrid}>
            {/* Profile 卡片 */}
            <TouchableOpacity
              style={styles.cardWrapper}
              onPress={() => handleCardPress('Profile')}
              activeOpacity={0.7}
            >
              <Card style={[styles.card, styles.profileCard, { backgroundColor: theme.colors.surfaceVariant }]}>
                <Card.Content style={styles.cardContent}>
                  <View style={[styles.iconContainer, { backgroundColor: theme.colors.primaryContainer }]}>
                    <MaterialIcons name="person" size={28} color={theme.colors.primary} />
                  </View>
                  <RNText style={[styles.cardText, { color: theme.colors.onSurface }]}>Profile</RNText>
                </Card.Content>
              </Card>
            </TouchableOpacity>

            {/* Edit Group 卡片 */}
            <TouchableOpacity
              style={styles.cardWrapper}
              onPress={() => handleCardPress('EditGroup')}
              activeOpacity={0.7}
            >
              <Card style={[styles.card, styles.editGroupCard, { backgroundColor: theme.colors.surfaceVariant }]}>
                <Card.Content style={styles.cardContent}>
                  <View style={[styles.iconContainer, { backgroundColor: theme.colors.secondaryContainer }]}>
                    <MaterialIcons name="edit" size={28} color={theme.colors.secondary} />
                  </View>
                  <RNText style={[styles.cardText, { color: theme.colors.onSurface }]}>Edit Group</RNText>
                </Card.Content>
              </Card>
            </TouchableOpacity>
          </View>

          {/* Quick Actions 區域 */}
          <Card style={[styles.quickActionsCard, { backgroundColor: theme.colors.surfaceVariant }]}>
            <Card.Content>
              <Text style={[styles.quickActionsTitle, { color: theme.colors.onSurface }]}>Quick Actions</Text>

              {/* Search User 按鈕 */}
              <TouchableOpacity
                style={[
                  styles.actionButton,
                  styles.searchUserButton,
                  {
                    backgroundColor: theme.colors.surface,
                    borderColor: theme.colors.outline,
                    borderWidth: 1,
                  }
                ]}
                onPress={() => handleQuickAction('SearchUser')}
                activeOpacity={0.7}
              >
                <View style={styles.actionButtonContent}>
                  <MaterialIcons name="search" size={24} color={theme.colors.tertiary} />
                  <RNText style={[styles.actionButtonLabel, { color: theme.colors.tertiary }]}>
                    Search User
                  </RNText>
                </View>
              </TouchableOpacity>

              {/* View Notifications 按鈕 */}
              <TouchableOpacity
                style={[
                  styles.actionButton,
                  styles.viewNotificationsButton,
                  {
                    backgroundColor: theme.colors.surface,
                    borderColor: theme.colors.outline,
                    borderWidth: 1,
                  }
                ]}
                onPress={() => handleQuickAction('ViewNotifications')}
                activeOpacity={0.7}
              >
                <View style={styles.actionButtonContent}>
                  <MaterialIcons name="visibility" size={24} color={theme.colors.secondary} />
                  <RNText style={[styles.actionButtonLabel, { color: theme.colors.secondary }]}>
                    View My Initiated Notifications
                  </RNText>
                </View>
              </TouchableOpacity>
            </Card.Content>
          </Card>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
}

// 樣式定義 - 遵循 Material Design 原則，使用主題顏色支持 Dark Mode
const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor 通過 theme.colors.background 動態設置
  },
  statusBarBackground: {
    height: Platform.OS === 'ios' ? 44 : 25, // 平台特定的狀態欄高度，Android 稍微調整
    // backgroundColor 通過 theme.colors.primary 動態設置
  },
  safeArea: {
    flex: 1,
  },
  header: {
    // backgroundColor 通過 theme.colors.primary 動態設置
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  headerContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logo: {
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  appTitle: {
    marginLeft: 12,
    fontSize: 20,
    // color 通過 theme.colors.onPrimary 動態設置
  },
  content: {
    flex: 1,
    padding: 24,
  },
  cardGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
    gap: 16,
  },
  cardWrapper: {
    flex: 1,
  },
  card: {
    // backgroundColor 通過 theme.colors.surfaceVariant 動態設置
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    borderRadius: 12,
    aspectRatio: 1, // 正方形卡片
  },
  profileCard: {
    // Profile 卡片特定樣式
  },
  editGroupCard: {
    // Edit Group 卡片特定樣式
  },
  cardContent: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20, // 調整間距以匹配設計
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
    // backgroundColor 通過主題顏色動態設置
  },
  cardText: {
    fontSize: 16,
    fontWeight: '800', // font-medium 對應 500
    textAlign: 'center',
    marginTop: 8,
    fontFamily: Platform.OS === 'ios' ? 'System' : undefined, // 平台特定系統字體
    lineHeight: Platform.OS === 'android' ? 20 : undefined, // Android 特定行高優化
    includeFontPadding: false, // Android 特定：移除字體內邊距以改善文字顯示
  },
  quickActionsCard: {
    // backgroundColor 通過 theme.colors.surfaceVariant 動態設置
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    borderRadius: 12,
    padding: 24,
  },
  quickActionsTitle: {
    fontSize: 18,
    fontWeight: '600',
    // color 通過 theme.colors.onSurface 動態設置
    marginBottom: 16,
  },
  actionButton: {
    marginBottom: 16,
    borderRadius: 8,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  searchUserButton: {
    // 使用主題顏色，不需要硬編碼背景色
  },
  viewNotificationsButton: {
    // 使用主題顏色，不需要硬編碼背景色
  },
  actionButtonContent: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    gap: 12,
  },
  actionButtonLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 12,
    flex: 1,
  },
});
