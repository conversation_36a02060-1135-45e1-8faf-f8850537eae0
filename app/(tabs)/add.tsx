import { MaterialIcons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState } from 'react';
import { Platform, ScrollView, StyleSheet, Text as RNText, View } from 'react-native';
import {
  Appbar,
  Button,
  Surface,
  Text,
  TextInput,
  useTheme,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

import RecipientsModal from '@/components/RecipientsModal';

// Case type options with icons
const CASE_TYPES = [
  {
    id: 'mother_baby',
    label: 'Mother and Baby Transfer',
    shortLabel: 'Mother & Baby',
    icon: 'family-restroom' as const // Material icon for mother and baby
  },
  {
    id: 'mother_only',
    label: 'Mother Only Transfer',
    shortLabel: 'Mother Only',
    icon: 'person' as const // Material icon for person/woman
  },
  {
    id: 'baby_nicu_scbu',
    label: 'Baby Transfer to NICU/SCBU',
    shortLabel: 'Baby to NICU',
    icon: 'child-care' as const // Material icon for baby/child
  },
] as const;

// All available recipients data (groups and individuals)
const ALL_STAFF_GROUPS = [
  { id: 'management', name: 'Management Team', icon: 'business' as const, color: '#8B5CF6', initials: 'MT' },
  { id: 'all_employees', name: 'All Employees', icon: 'group' as const, color: '#3B82F6', initials: 'AE' },
  { id: 'project_alpha', name: 'Project Alpha Team', icon: 'group' as const, color: '#10B981', initials: 'PA' },
];

const ALL_INDIVIDUAL_STAFF = [
  { id: '1', name: 'Jane Doe', role: 'Obstetrician', initials: 'JD', color: '#3B82F6' },
  { id: '2', name: 'Sarah Miller', role: 'Midwife', initials: 'SM', color: '#8B5CF6' },
  { id: '3', name: 'Paul Allen', role: 'Anaesthetist', initials: 'PA', color: '#10B981' },
  { id: '4', name: 'Alice Johnson', role: 'Nurse', initials: 'AJ', color: '#F59E0B' },
  { id: '5', name: 'Robert Brown', role: 'Pediatrician', initials: 'RB', color: '#EF4444' },
  { id: '6', name: 'Emily Davis', role: 'Surgeon', initials: 'ED', color: '#06B6D4' },
];

// Notification creation screen - allows users to build specific notification details
export default function InitiateNotificationScreen() {
  // Get current theme to support Dark Mode
  const theme = useTheme();

  // Form state management
  const [selectedCaseType, setSelectedCaseType] = useState<string>('');
  const [motherInitial, setMotherInitial] = useState('');
  const [bedNumber, setBedNumber] = useState('');
  const [ward, setWard] = useState('');
  const [clinicalNotes, setClinicalNotes] = useState('');
  const [isNotesExpanded, setIsNotesExpanded] = useState(false);
  const [selectedRecipients, setSelectedRecipients] = useState<string[]>([]);
  const [showRecipientsModal, setShowRecipientsModal] = useState(false);

  // Handle close/back operation
  const handleClose = () => {
    router.back();
  };

  // Handle case type selection
  const handleCaseTypeSelect = (caseTypeId: string) => {
    setSelectedCaseType(caseTypeId);
  };

  // Handle recipients selection
  const handleRecipientsSelect = () => {
    setShowRecipientsModal(true);
  };

  // Handle recipients modal dismiss
  const handleRecipientsModalDismiss = () => {
    setShowRecipientsModal(false);
  };

  // Handle recipients change
  const handleRecipientsChange = (recipients: string[]) => {
    setSelectedRecipients(recipients);
  };

  // Handle send notification
  const handleSendNotification = () => {
    console.log('Sending notification...', {
      caseType: selectedCaseType,
      motherInitial,
      bedNumber,
      ward,
      clinicalNotes,
      recipients: selectedRecipients,
    });
    // TODO: Implement send logic
  };

  // Check if required fields are filled
  const isFormValid = selectedCaseType && motherInitial.trim() && bedNumber.trim() && ward.trim();

  // Get field background color based on completion status
  const getFieldBackgroundColor = (value: string, isRequired: boolean = false) => {
    if (!isRequired) return theme.colors.surface;
    if (value.trim()) {
      return theme.dark ? '#1B4332' : '#D1FAE5'; // Dark green for dark mode, light green for light mode
    }
    return theme.colors.surface;
  };

  // Get selected recipients data for display
  const getSelectedRecipientsData = () => {
    const selectedData: { id: string; name: string; initials: string; color: string }[] = [];

    selectedRecipients.forEach(id => {
      // Check if it's a group
      const group = ALL_STAFF_GROUPS.find(g => g.id === id);
      if (group) {
        selectedData.push({
          id: group.id,
          name: group.name,
          initials: group.initials,
          color: group.color
        });
        return;
      }

      // Check if it's an individual staff member
      const staff = ALL_INDIVIDUAL_STAFF.find(s => s.id === id);
      if (staff) {
        selectedData.push({
          id: staff.id,
          name: staff.name,
          initials: staff.initials,
          color: staff.color
        });
      }
    });

    return selectedData;
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header with close button */}
      <Appbar.Header style={{ backgroundColor: theme.colors.surface }}>
        <Appbar.Action
          icon="close"
          onPress={handleClose}
          iconColor={theme.colors.onSurface}
        />
        <Appbar.Content
          title="New Notification"
          titleStyle={[styles.headerTitle, { color: theme.colors.onSurface }]}
        />
      </Appbar.Header>

      {/* Main content area */}
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainer}
      >
        {/* Case type button group - Xiaohongshu style */}
        <View style={styles.section}>
          <Text variant="titleSmall" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Case Type
          </Text>
          <View style={styles.caseTypeButtonGroup}>
            {CASE_TYPES.map((caseType) => (
              <Button
                key={caseType.id}
                mode={selectedCaseType === caseType.id ? 'contained' : 'outlined'}
                onPress={() => handleCaseTypeSelect(caseType.id)}
                style={[
                  styles.caseTypeButton,
                  selectedCaseType === caseType.id && { backgroundColor: theme.colors.primary }
                ]}
                labelStyle={[
                  styles.caseTypeButtonLabel,
                  {
                    color: selectedCaseType === caseType.id
                      ? theme.colors.onPrimary
                      : theme.colors.onSurface
                  }
                ]}
                contentStyle={styles.caseTypeButtonContent}
                icon={({ size }) => (
                  <MaterialIcons
                    name={caseType.icon}
                    size={size}
                    color={selectedCaseType === caseType.id ? theme.colors.onPrimary : theme.colors.onSurface}
                  />
                )}
              >
                {caseType.shortLabel}
              </Button>
            ))}
          </View>
        </View>

        {/* Mother's Details */}
        <View style={styles.section}>
          <Text variant="titleSmall" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Mother&apos;s Details
          </Text>
          <View style={styles.motherDetailsRow}>
            <View style={styles.halfWidthContainer}>
              <View style={styles.labelContainer}>
                <Text style={[styles.inputLabel, { color: theme.colors.onSurface }]}>Initial</Text>
                <Text style={styles.requiredAsterisk}>*</Text>
              </View>
              <TextInput
                value={motherInitial}
                onChangeText={setMotherInitial}
                style={[
                  styles.halfWidthInput,
                  { backgroundColor: getFieldBackgroundColor(motherInitial, true) }
                ]}
                mode="outlined"
                placeholder="Enter initial"
                dense
              />
            </View>
            <View style={styles.halfWidthContainer}>
              <View style={styles.labelContainer}>
                <Text style={[styles.inputLabel, { color: theme.colors.onSurface }]}>Bed Number</Text>
                <Text style={styles.requiredAsterisk}>*</Text>
              </View>
              <TextInput
                value={bedNumber}
                onChangeText={setBedNumber}
                style={[
                  styles.halfWidthInput,
                  { backgroundColor: getFieldBackgroundColor(bedNumber, true) }
                ]}
                mode="outlined"
                placeholder="Enter bed number"
                dense
              />
            </View>
          </View>
        </View>

        {/* Designated Ward */}
        <View style={styles.section}>
          <View style={styles.labelContainer}>
            <Text style={[styles.inputLabel, { color: theme.colors.onSurface }]}>Designated Ward</Text>
            <Text style={styles.requiredAsterisk}>*</Text>
          </View>
          <TextInput
            value={ward}
            onChangeText={setWard}
            style={[
              styles.fullWidthInput,
              { backgroundColor: getFieldBackgroundColor(ward, true) }
            ]}
            mode="outlined"
            placeholder="Enter ward name"
            dense
          />
        </View>

        {/* Clinical Notes */}
        <View style={styles.section}>
          <Button
            mode="outlined"
            onPress={() => setIsNotesExpanded(!isNotesExpanded)}
            style={[styles.expandButton, { borderColor: theme.colors.outline }]}
            contentStyle={styles.expandButtonContent}
            labelStyle={{ color: theme.colors.onSurface }}
            icon={({ size }) => (
              <MaterialIcons
                name={isNotesExpanded ? "expand-less" : "expand-more"}
                size={size}
                color={theme.colors.onSurface}
              />
            )}
          >
            Add Clinical Note (Optional)
          </Button>
          {isNotesExpanded && (
            <TextInput
              value={clinicalNotes}
              onChangeText={setClinicalNotes}
              style={[styles.notesInput, { backgroundColor: getFieldBackgroundColor(clinicalNotes, false) }]}
              mode="outlined"
              placeholder="Enter clinical notes (optional)..."
              multiline
              numberOfLines={8}
              dense
            />
          )}
        </View>

        {/* Recipients */}
        <View style={styles.section}>
          <Text variant="titleSmall" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Recipients
          </Text>
          <View style={styles.recipientsList}>
            {getSelectedRecipientsData().map((recipient) => (
              <View key={recipient.id} style={styles.recipientItem}>
                <View style={[styles.recipientAvatar, { backgroundColor: recipient.color }]}>
                  <RNText style={styles.recipientInitials}>{recipient.initials}</RNText>
                </View>
                <RNText style={[styles.recipientText, { color: theme.colors.onSurface }]}>
                  {recipient.name}
                </RNText>
              </View>
            ))}
            {getSelectedRecipientsData().length === 0 && (
              <RNText style={[styles.noRecipientsText, { color: theme.colors.onSurfaceVariant }]}>
                No recipients selected
              </RNText>
            )}
          </View>
          <Button
            mode="outlined"
            onPress={handleRecipientsSelect}
            style={[styles.selectRecipientsButton, { borderColor: theme.colors.outline }]}
            contentStyle={styles.selectRecipientsContent}
            labelStyle={{ color: theme.colors.onSurface }}
            icon={({ size }) => (
              <MaterialIcons name="group-add" size={size} color={theme.colors.onSurface} />
            )}
          >
            Select Recipients
          </Button>
        </View>
      </ScrollView>

      {/* Bottom send button */}
      <Surface style={[styles.bottomContainer, { backgroundColor: theme.colors.surface }]} elevation={4}>
        <Button
          mode="contained"
          onPress={handleSendNotification}
          style={[
            styles.sendButton,
            {
              backgroundColor: isFormValid ? theme.colors.primary : theme.colors.surfaceVariant,
              opacity: isFormValid ? 1 : 0.6
            }
          ]}
          contentStyle={styles.sendButtonContent}
          labelStyle={[
            styles.sendButtonLabel,
            { color: isFormValid ? theme.colors.onPrimary : theme.colors.onSurfaceVariant }
          ]}
          disabled={!isFormValid}
          loading={false}
        >
          Send Notification
        </Button>
      </Surface>

      {/* Recipients Selection Modal */}
      <RecipientsModal
        visible={showRecipientsModal}
        onDismiss={handleRecipientsModalDismiss}
        selectedRecipients={selectedRecipients}
        onRecipientsChange={handleRecipientsChange}
      />
    </SafeAreaView>
  );
}

// Style definitions - using theme colors to support Dark Mode and platform-specific fonts
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 100, // Leave space for bottom button
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  // Case type button group styles - Vertical layout for better text display
  caseTypeButtonGroup: {
    flexDirection: 'column',
    gap: 12,
  },
  caseTypeButton: {
    borderRadius: 12,
    minHeight: 48,
  },
  caseTypeButtonContent: {
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  caseTypeButtonLabel: {
    fontSize: 14,
    fontWeight: '500',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
    ...(Platform.OS === 'android' && {
      lineHeight: 18,
      includeFontPadding: false,
    }),
  },
  // Mother's details styles
  motherDetailsRow: {
    flexDirection: 'row',
    gap: 12,
  },
  halfWidthContainer: {
    flex: 1,
  },
  halfWidthInput: {
    flex: 1,
  },
  // Label and required field styles
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  requiredAsterisk: {
    color: '#EF4444', // Red color for asterisk
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  fullWidthInput: {
    width: '100%',
  },
  // Clinical notes styles
  expandButton: {
    borderRadius: 12,
    marginBottom: 8,
  },
  expandButtonContent: {
    paddingVertical: 8,
    justifyContent: 'space-between',
    flexDirection: 'row-reverse',
  },
  notesInput: {
    marginTop: 8,
  },
  // Recipients styles
  recipientsList: {
    marginBottom: 16,
  },
  recipientItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  recipientAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  recipientInitials: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
    ...(Platform.OS === 'android' && {
      lineHeight: 16,
      includeFontPadding: false,
    }),
  },
  recipientText: {
    fontSize: 16,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
    ...(Platform.OS === 'android' && {
      lineHeight: 20,
      includeFontPadding: false,
    }),
  },
  noRecipientsText: {
    fontSize: 14,
    fontStyle: 'italic',
    textAlign: 'center',
    paddingVertical: 16,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
    ...(Platform.OS === 'android' && {
      lineHeight: 18,
      includeFontPadding: false,
    }),
  },
  selectRecipientsButton: {
    borderRadius: 12,
  },
  selectRecipientsContent: {
    paddingVertical: 8,
    justifyContent: 'center',
  },
  // Bottom button styles
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    paddingBottom: Platform.OS === 'ios' ? 34 : 16, // Consider safe area
  },
  sendButton: {
    borderRadius: 12,
    minHeight: 48,
  },
  sendButtonContent: {
    paddingVertical: 12,
  },
  sendButtonLabel: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
    ...(Platform.OS === 'android' && {
      lineHeight: 20,
      includeFontPadding: false,
    }),
  },
});
