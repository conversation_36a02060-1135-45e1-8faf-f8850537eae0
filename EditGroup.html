<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>Edit Group</title>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&amp;display=swap" rel="stylesheet"/>
<style>
        body {
            font-family: 'Roboto', sans-serif;
        }
    </style>
<style>
        body {
            min-height: max(884px, 100dvh);
        }
    </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-gray-100">
<div class="container mx-auto max-w-md bg-white min-h-screen">
<header class="flex items-center justify-between p-4 border-b border-gray-200">
<div class="flex items-center">
<span class="material-icons text-gray-700">arrow_back</span>
<h1 class="text-xl font-semibold ml-4 text-gray-800">Edit Group</h1>
</div>
</header>
<div class="p-4">
<label class="block text-sm font-medium text-gray-700 mb-1" for="group-name">Group Name</label>
<input class="w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500" id="group-name" type="text" value="All Employees"/>
</div>
<div class="p-4">
<div class="flex items-center justify-between mb-3">
<h2 class="text-lg font-semibold text-gray-800">Members (3)</h2>
<button class="text-blue-600 font-medium flex items-center">
<span class="material-icons mr-1">add_circle_outline</span>
                    Add Member
                </button>
</div>
<div class="space-y-3">
<div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg shadow-sm">
<div class="flex items-center">
<div class="w-10 h-10 rounded-full bg-purple-500 flex items-center justify-center text-white font-semibold text-sm">
                            AJ
                        </div>
<span class="ml-3 text-gray-700 font-medium">Alice Johnson</span>
</div>
<button class="text-red-500">
<span class="material-icons">remove_circle_outline</span>
</button>
</div>
<div class="flex items-center justify-between p-3 bg-blue-500 rounded-lg shadow-sm text-white">
<div class="flex items-center">
<div class="w-10 h-10 rounded-full bg-white flex items-center justify-center text-blue-500 font-semibold text-sm">
                            RB
                        </div>
<span class="ml-3 font-medium">Robert Brown</span>
</div>
<button class="text-white opacity-75 hover:opacity-100">
<span class="material-icons">remove_circle_outline</span>
</button>
</div>
<div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg shadow-sm">
<div class="flex items-center">
<div class="w-10 h-10 rounded-full bg-teal-500 flex items-center justify-center text-white font-semibold text-sm">
                            ED
                        </div>
<span class="ml-3 text-gray-700 font-medium">Emily Davis</span>
</div>
<button class="text-red-500">
<span class="material-icons">remove_circle_outline</span>
</button>
</div>
</div>
</div>
<div class="p-4 mt-4">
<h2 class="text-lg font-semibold text-gray-800 mb-3">Other Contacts (2)</h2>
<div class="space-y-3">
<div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg shadow-sm">
<div class="flex items-center">
<div class="w-10 h-10 rounded-full bg-orange-500 flex items-center justify-center text-white font-semibold text-sm">
                            CS
                        </div>
<span class="ml-3 text-gray-700 font-medium">Carol Smith</span>
</div>
<button class="text-blue-600">
<span class="material-icons">add_circle_outline</span>
</button>
</div>
<div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg shadow-sm">
<div class="flex items-center">
<div class="w-10 h-10 rounded-full bg-pink-500 flex items-center justify-center text-white font-semibold text-sm">
                            MW
                        </div>
<span class="ml-3 text-gray-700 font-medium">Michael Wilson</span>
</div>
<button class="text-blue-600">
<span class="material-icons">add_circle_outline</span>
</button>
</div>
</div>
</div>
<div class="p-4 mt-8 pb-8">
<button class="w-full py-3 bg-red-50 text-red-600 font-semibold rounded-lg flex items-center justify-center mb-4">
<span class="material-icons mr-2">delete_outline</span>
                Delete Group
            </button>
<button class="w-full py-3 bg-blue-600 text-white font-semibold rounded-lg flex items-center justify-center">
                Save
            </button>
</div>
</div>

</body></html>