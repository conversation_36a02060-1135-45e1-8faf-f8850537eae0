import { MaterialIcons } from '@expo/vector-icons';
import React, { useState } from 'react';
import { Platform, ScrollView, StyleSheet, Text as RNText, TouchableOpacity, View } from 'react-native';
import {
  Appbar,
  Button,
  Checkbox,
  Divider,
  Modal,
  Portal,
  Searchbar,
  Surface,
  Text,
  useTheme,
} from 'react-native-paper';

// Medical staff groups
const STAFF_GROUPS = [
  { id: 'management', name: 'Management Team', icon: 'business' as const, color: '#8B5CF6' },
  { id: 'all_employees', name: 'All Employees', icon: 'group' as const, color: '#3B82F6' },
  { id: 'project_alpha', name: 'Project Alpha Team', icon: 'group' as const, color: '#10B981' },
];

// Individual staff members
const INDIVIDUAL_STAFF = [
  { id: '1', name: '<PERSON>', role: 'Obstetrician', initials: 'J<PERSON>', color: '#3B82F6' },
  { id: '2', name: '<PERSON>', role: 'Midwife', initials: 'SM', color: '#8B5CF6' },
  { id: '3', name: '<PERSON>', role: 'Anaesthetist', initials: '<PERSON>', color: '#10B981' },
  { id: '4', name: 'Alice Johnson', role: 'Nurse', initials: 'AJ', color: '#F59E0B' },
  { id: '5', name: 'Robert Brown', role: 'Pediatrician', initials: 'RB', color: '#EF4444' },
  { id: '6', name: 'Emily Davis', role: 'Surgeon', initials: 'ED', color: '#06B6D4' },
];

interface RecipientsModalProps {
  visible: boolean;
  onDismiss: () => void;
  selectedRecipients: string[];
  onRecipientsChange: (recipients: string[]) => void;
}

// Recipients selection modal component - based on Design3.html patterns
export default function RecipientsModal({
  visible,
  onDismiss,
  selectedRecipients,
  onRecipientsChange,
}: RecipientsModalProps) {
  // Get current theme to support Dark Mode
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');

  // Handle group selection
  const handleGroupToggle = (groupId: string) => {
    const isSelected = selectedRecipients.includes(groupId);
    if (isSelected) {
      onRecipientsChange(selectedRecipients.filter(id => id !== groupId));
    } else {
      onRecipientsChange([...selectedRecipients, groupId]);
    }
  };

  // Handle individual staff selection
  const handleStaffToggle = (staffId: string) => {
    const isSelected = selectedRecipients.includes(staffId);
    if (isSelected) {
      onRecipientsChange(selectedRecipients.filter(id => id !== staffId));
    } else {
      onRecipientsChange([...selectedRecipients, staffId]);
    }
  };

  // Filter groups based on search query
  const filteredGroups = STAFF_GROUPS.filter(group =>
    group.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Filter staff based on search query
  const filteredStaff = INDIVIDUAL_STAFF.filter(staff =>
    staff.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    staff.role.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Handle done button
  const handleDone = () => {
    onDismiss();
  };

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={[
          styles.modalContainer,
          { backgroundColor: theme.colors.surface }
        ]}
      >
        {/* Header */}
        <Appbar.Header style={{ backgroundColor: theme.colors.surface }}>
          <Appbar.BackAction onPress={onDismiss} />
          <Appbar.Content
            title="Select Recipients"
            titleStyle={[styles.headerTitle, { color: theme.colors.onSurface }]}
          />
        </Appbar.Header>

        {/* Search bar */}
        <View style={styles.searchContainer}>
          <Searchbar
            placeholder="Search by name or role"
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={[styles.searchBar, { backgroundColor: theme.colors.surfaceVariant }]}
            inputStyle={{ color: theme.colors.onSurface }}
            iconColor={theme.colors.onSurfaceVariant}
          />
        </View>

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Groups Section */}
          <View style={styles.section}>
            <Surface style={[styles.sectionHeader, { backgroundColor: theme.colors.primaryContainer }]}>
              <Text style={[styles.sectionHeaderText, { color: theme.colors.primary }]}>
                GROUPS
              </Text>
            </Surface>
            {filteredGroups.map((group) => (
              <Surface
                key={group.id}
                style={[
                  styles.listItem,
                  { backgroundColor: theme.colors.surface },
                  selectedRecipients.includes(group.id) && {
                    backgroundColor: theme.colors.primaryContainer
                  }
                ]}
                elevation={selectedRecipients.includes(group.id) ? 2 : 1}
              >
                <TouchableOpacity
                  style={styles.listItemTouchable}
                  onPress={() => handleGroupToggle(group.id)}
                  activeOpacity={0.7}
                >
                  <View style={styles.listItemContent}>
                    <View style={styles.listItemLeft}>
                      <View style={[styles.groupAvatar, { backgroundColor: group.color }]}>
                        <MaterialIcons name={group.icon} size={20} color="#FFFFFF" />
                      </View>
                      <Text style={[
                        styles.listItemText,
                        { color: selectedRecipients.includes(group.id) ? theme.colors.primary : theme.colors.onSurface }
                      ]}>
                        {group.name}
                      </Text>
                    </View>
                    <Checkbox
                      status={selectedRecipients.includes(group.id) ? 'checked' : 'unchecked'}
                      onPress={() => handleGroupToggle(group.id)}
                    />
                  </View>
                </TouchableOpacity>
              </Surface>
            ))}
          </View>

          {/* Individuals Section */}
          <View style={styles.section}>
            <Surface style={[styles.sectionHeader, { backgroundColor: theme.colors.secondaryContainer }]}>
              <Text style={[styles.sectionHeaderText, { color: theme.colors.secondary }]}>
                INDIVIDUALS
              </Text>
            </Surface>
            {filteredStaff.map((staff) => (
              <Surface
                key={staff.id}
                style={[
                  styles.listItem,
                  { backgroundColor: theme.colors.surface },
                  selectedRecipients.includes(staff.id) && {
                    backgroundColor: theme.colors.primaryContainer
                  }
                ]}
                elevation={selectedRecipients.includes(staff.id) ? 2 : 1}
              >
                <TouchableOpacity
                  style={styles.listItemTouchable}
                  onPress={() => handleStaffToggle(staff.id)}
                  activeOpacity={0.7}
                >
                  <View style={styles.listItemContent}>
                    <View style={styles.listItemLeft}>
                      <View style={[styles.staffAvatar, { backgroundColor: staff.color }]}>
                        <RNText style={styles.staffInitials}>{staff.initials}</RNText>
                      </View>
                      <Text style={[
                        styles.listItemText,
                        { color: selectedRecipients.includes(staff.id) ? theme.colors.primary : theme.colors.onSurface }
                      ]}>
                        {staff.name}
                      </Text>
                    </View>
                    <Checkbox
                      status={selectedRecipients.includes(staff.id) ? 'checked' : 'unchecked'}
                      onPress={() => handleStaffToggle(staff.id)}
                    />
                  </View>
                </TouchableOpacity>
              </Surface>
            ))}
          </View>
        </ScrollView>

        {/* Bottom button */}
        <View style={styles.bottomContainer}>
          <Divider style={{ backgroundColor: theme.colors.outline }} />
          <Button
            mode="contained"
            onPress={handleDone}
            style={[styles.doneButton, { backgroundColor: theme.colors.primary }]}
            contentStyle={styles.doneButtonContent}
            labelStyle={[styles.doneButtonLabel, { color: theme.colors.onPrimary }]}
          >
            Done ({selectedRecipients.length})
          </Button>
        </View>
      </Modal>
    </Portal>
  );
}

// Style definitions - using theme colors to support Dark Mode and platform-specific fonts
const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    margin: 0,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  searchContainer: {
    padding: 16,
    paddingBottom: 8,
  },
  searchBar: {
    elevation: 0,
    borderRadius: 12,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    marginBottom: 12,
    alignSelf: 'flex-start',
  },
  sectionHeaderText: {
    fontSize: 12,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  listItem: {
    borderRadius: 12,
    marginBottom: 8,
    padding: 12,
  },
  listItemTouchable: {
    flex: 1,
  },
  listItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  listItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  groupAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  staffAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  staffInitials: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  listItemText: {
    fontSize: 16,
    fontWeight: '500',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },

  bottomContainer: {
    padding: 16,
    paddingBottom: Platform.OS === 'ios' ? 34 : 16,
  },
  doneButton: {
    borderRadius: 12,
    marginTop: 12,
  },
  doneButtonContent: {
    paddingVertical: 8,
  },
  doneButtonLabel: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
});
