import { MaterialIcons } from '@expo/vector-icons';
import React, { useState } from 'react';
import { Platform, ScrollView, StyleSheet, Text as RNText, TouchableOpacity, View } from 'react-native';
import {
  <PERSON>ppbar,
  Button,
  Divider,
  Modal,
  Portal,
  Surface,
  Text,
  TextInput,
  useTheme,
} from 'react-native-paper';

// Available staff members for group management
const AVAILABLE_STAFF = [
  { id: '1', name: '<PERSON>', role: 'O<PERSON>tetrician', initials: 'J<PERSON>', color: '#3B82F6' },
  { id: '2', name: '<PERSON>', role: 'Midwife', initials: '<PERSON>', color: '#8B5CF6' },
  { id: '3', name: '<PERSON>', role: 'Anaesthetist', initials: '<PERSON>', color: '#10B981' },
  { id: '4', name: '<PERSON>', role: 'Nurse', initials: '<PERSON>', color: '#F59E0B' },
  { id: '5', name: '<PERSON>', role: 'Pediatrician', initials: '<PERSON>', color: '#EF4444' },
  { id: '6', name: '<PERSON>', role: 'Sur<PERSON>', initials: 'ED', color: '#06B6D4' },
  { id: '7', name: 'Carol <PERSON>', role: 'Nurse', initials: 'CS', color: '#F97316' },
  { id: '8', name: 'Michael Wilson', role: 'Technician', initials: 'MW', color: '#EC4899' },
];

// Default group for editing (can be passed as prop in real implementation)
const DEFAULT_GROUP = {
  id: 'all_employees',
  name: 'All Employees',
  memberIds: ['4', '5', '6'], // Alice Johnson, Robert Brown, Emily Davis
};

interface EditGroupModalProps {
  visible: boolean;
  onDismiss: () => void;
  onSave?: (groupData: { name: string; memberIds: string[] }) => void;
  onDelete?: () => void;
}

// Edit Group modal component - based on EditGroup.html patterns
export default function EditGroupModal({
  visible,
  onDismiss,
  onSave,
  onDelete,
}: EditGroupModalProps) {
  // Get current theme to support Dark Mode
  const theme = useTheme();
  
  // Form state
  const [groupName, setGroupName] = useState(DEFAULT_GROUP.name);
  const [memberIds, setMemberIds] = useState<string[]>(DEFAULT_GROUP.memberIds);

  // Get current members and available members
  const currentMembers = AVAILABLE_STAFF.filter(staff => memberIds.includes(staff.id));
  const availableMembers = AVAILABLE_STAFF.filter(staff => !memberIds.includes(staff.id));

  // Handle adding member to group
  const handleAddMember = (staffId: string) => {
    setMemberIds([...memberIds, staffId]);
  };

  // Handle removing member from group
  const handleRemoveMember = (staffId: string) => {
    setMemberIds(memberIds.filter(id => id !== staffId));
  };

  // Handle save
  const handleSave = () => {
    if (onSave) {
      onSave({ name: groupName, memberIds });
    }
    onDismiss();
  };

  // Handle delete
  const handleDelete = () => {
    if (onDelete) {
      onDelete();
    }
    onDismiss();
  };

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={[
          styles.modalContainer,
          { backgroundColor: theme.colors.surface }
        ]}
      >
        {/* Header */}
        <Appbar.Header style={{ backgroundColor: theme.colors.surface }}>
          <Appbar.BackAction onPress={onDismiss} />
          <Appbar.Content
            title="Edit Group"
            titleStyle={[styles.headerTitle, { color: theme.colors.onSurface }]}
          />
        </Appbar.Header>

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Group Name Input */}
          <View style={styles.section}>
            <Text style={[styles.sectionLabel, { color: theme.colors.onSurface }]}>
              Group Name
            </Text>
            <TextInput
              value={groupName}
              onChangeText={setGroupName}
              style={[styles.groupNameInput, { backgroundColor: theme.colors.surface }]}
              mode="outlined"
              placeholder="Enter group name"
              dense
            />
          </View>

          {/* Current Members Section */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                Members ({currentMembers.length})
              </Text>
            </View>
            {currentMembers.map((member) => (
              <Surface
                key={member.id}
                style={[
                  styles.memberItem,
                  { backgroundColor: theme.colors.surfaceVariant }
                ]}
                elevation={1}
              >
                <View style={styles.memberContent}>
                  <View style={styles.memberLeft}>
                    <View style={[styles.memberAvatar, { backgroundColor: member.color }]}>
                      <RNText style={styles.memberInitials}>{member.initials}</RNText>
                    </View>
                    <Text style={[styles.memberText, { color: theme.colors.onSurface }]}>
                      {member.name}
                    </Text>
                  </View>
                  <TouchableOpacity
                    onPress={() => handleRemoveMember(member.id)}
                    style={styles.actionButton}
                  >
                    <MaterialIcons name="remove-circle-outline" size={24} color={theme.colors.error} />
                  </TouchableOpacity>
                </View>
              </Surface>
            ))}
          </View>

          {/* Available Members Section */}
          {availableMembers.length > 0 && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                Other Contacts ({availableMembers.length})
              </Text>
              {availableMembers.map((member) => (
                <Surface
                  key={member.id}
                  style={[
                    styles.memberItem,
                    { backgroundColor: theme.colors.surface }
                  ]}
                  elevation={1}
                >
                  <View style={styles.memberContent}>
                    <View style={styles.memberLeft}>
                      <View style={[styles.memberAvatar, { backgroundColor: member.color }]}>
                        <RNText style={styles.memberInitials}>{member.initials}</RNText>
                      </View>
                      <Text style={[styles.memberText, { color: theme.colors.onSurface }]}>
                        {member.name}
                      </Text>
                    </View>
                    <TouchableOpacity
                      onPress={() => handleAddMember(member.id)}
                      style={styles.actionButton}
                    >
                      <MaterialIcons name="add-circle-outline" size={24} color={theme.colors.primary} />
                    </TouchableOpacity>
                  </View>
                </Surface>
              ))}
            </View>
          )}
        </ScrollView>

        {/* Bottom Actions */}
        <View style={styles.bottomContainer}>
          <Divider style={{ backgroundColor: theme.colors.outline }} />
          
          {/* Delete Button */}
          <Button
            mode="outlined"
            onPress={handleDelete}
            style={[
              styles.deleteButton,
              { 
                borderColor: theme.colors.error,
                backgroundColor: theme.dark ? 'rgba(244, 67, 54, 0.1)' : '#FFEBEE'
              }
            ]}
            contentStyle={styles.deleteButtonContent}
            labelStyle={[styles.deleteButtonLabel, { color: theme.colors.error }]}
            icon={({ size }) => (
              <MaterialIcons name="delete-outline" size={size} color={theme.colors.error} />
            )}
          >
            Delete Group
          </Button>

          {/* Save Button */}
          <Button
            mode="contained"
            onPress={handleSave}
            style={[styles.saveButton, { backgroundColor: theme.colors.primary }]}
            contentStyle={styles.saveButtonContent}
            labelStyle={[styles.saveButtonLabel, { color: theme.colors.onPrimary }]}
            disabled={!groupName.trim()}
          >
            Save
          </Button>
        </View>
      </Modal>
    </Portal>
  );
}

// Style definitions - using theme colors to support Dark Mode and platform-specific fonts
const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    margin: 0,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  groupNameInput: {
    marginBottom: 8,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  memberItem: {
    borderRadius: 12,
    marginBottom: 8,
    padding: 12,
  },
  memberContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  memberLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  memberAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  memberInitials: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  memberText: {
    fontSize: 16,
    fontWeight: '500',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  actionButton: {
    padding: 4,
  },
  bottomContainer: {
    padding: 16,
    paddingBottom: Platform.OS === 'ios' ? 34 : 16,
  },
  deleteButton: {
    borderRadius: 12,
    marginTop: 12,
    marginBottom: 8,
  },
  deleteButtonContent: {
    paddingVertical: 8,
  },
  deleteButtonLabel: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  saveButton: {
    borderRadius: 12,
    marginTop: 8,
  },
  saveButtonContent: {
    paddingVertical: 8,
  },
  saveButtonLabel: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
});
